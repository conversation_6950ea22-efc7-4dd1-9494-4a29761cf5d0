# Oso Cloud Client Library Documentation Migration Analysis

### 1a: Policy API Content

```json
{
  "policy_api": {
    "update_policy": {
      "nodejs": {
        "method": "oso.policy(policy)",
        "description": "Updates the policy in Oso Cloud. Runs tests and fails if tests don't pass.",
        "example": "await oso.policy(\"actor User {}\")",
        "return_type": "Promise<void>"
      },
      "python": {
        "method": "oso.policy(policy)",
        "description": "Updates the policy in Oso Cloud. Runs tests and fails if tests don't pass.",
        "example": "oso.policy(\"actor User {}\")",
        "return_type": "None"
      },
      "go": {
        "method": "osoClient.Policy(policy)",
        "description": "Updates the policy in Oso Cloud. Runs tests and fails if tests don't pass.",
        "example": "e := osoClient.Policy(\"actor User {}\")",
        "return_type": "error"
      },
      "java": {
        "method": "oso.policy(policy)",
        "description": "Updates the policy in Oso Cloud. Runs tests and throws exception if tests fail.",
        "example": "oso.policy(\"allow(actor, action, resource) if\\n  has_permission(actor, action, resource);\\nactor User {}\");",
        "return_type": "void (throws ApiException)"
      },
      "ruby": {
        "method": "oso.policy(policy)",
        "description": "Updates the policy in Oso Cloud. Runs tests and fails if tests don't pass.",
        "example": "oso.policy(\"actor User {}\")",
        "return_type": "void"
      },
      "dotnet": {
        "method": "oso.Policy(policy)",
        "description": "Updates the policy in Oso Cloud. Runs tests and fails if tests don't pass.",
        "example": "oso.Policy(\"actor User {}\")",
        "return_type": "void"
      }
    },
    "get_policy_metadata": {
      "nodejs": {
        "method": "oso.getPolicyMetadata()",
        "description": "Returns metadata about the currently active policy",
        "example": "metadata = await oso.getPolicyMetadata();",
        "return_type": "Promise<PolicyMetadata>"
      },
      "python": {
        "method": "oso.get_policy_metadata()",
        "description": "Returns metadata about the currently active policy",
        "example": "metadata = oso.get_policy_metadata()",
        "return_type": "PolicyMetadata"
      },
      "go": {
        "method": "osoClient.GetPolicyMetadata()",
        "description": "Returns metadata about the currently active policy",
        "example": "metadata, err := osoClient.getPolicyMetadata()",
        "return_type": "(PolicyMetadata, error)"
      },
      "java": {
        "method": "oso.policyMetadata()",
        "description": "Returns metadata about the currently active policy",
        "example": "PolicyMetadata metadata = oso.policyMetadata();",
        "return_type": "PolicyMetadata"
      },
      "ruby": {
        "method": "oso.get_policy_metadata",
        "description": "Returns metadata about the currently active policy",
        "example": "metadata = oso.get_policy_metadata",
        "return_type": "PolicyMetadata"
      },
      "dotnet": {
        "method": "oso.GetPolicyMetadata()",
        "description": "Returns metadata about the currently active policy",
        "example": "metadata = await oso.GetPolicyMetadata()",
        "return_type": "Task<PolicyMetadata>"
      }
    }
  }
}
```

### 1b: Facts API Content

```json
{
  "facts_api": {
    "insert_fact": {
      "nodejs": {
        "current_method": "oso.insert([name, ...args])",
        "deprecated_method": "oso.tell(name, ...args)",
        "description": "Adds a fact named 'name' with the provided arguments",
        "example": "await oso.insert([\"has_role\", user, \"member\", repo])",
        "migration_note": "Arguments now wrapped in array"
      },
      "python": {
        "current_method": "oso.insert((name, *args))",
        "deprecated_method": "oso.tell(name, *args)",
        "description": "Adds a fact named 'name' with the provided arguments",
        "example": "oso.insert((\"has_role\", user, \"member\", repo))",
        "migration_note": "Arguments now in tuple format, use Value objects"
      },
      "go": {
        "current_method": "osoClient.Insert(fact)",
        "deprecated_method": "osoClient.Tell(name, ...args)",
        "description": "Adds the given fact to Oso Cloud",
        "example": "e := osoClient.Insert(oso.NewFact(\"has_role\", user, role, resource))",
        "migration_note": "Use oso.NewFact() helper, replace Instance with Value"
      },
      "java": {
        "current_method": "oso.insert(Fact)",
        "deprecated_method": "oso.tell(name, ...args)",
        "description": "Adds a single fact",
        "example": "oso.insert(new Fact(\"has_role\", new Value(\"User\", \"bob\"), new Value(\"owner\"), new Value(\"Organization\", \"acme\")))",
        "migration_note": "Wrap arguments in Fact object, update imports"
      },
      "ruby": {
        "current_method": "oso.tell(name, *args)",
        "deprecated_method": null,
        "description": "Adds a fact named 'name' with the provided arguments",
        "example": "oso.tell(\"has_role\", user, role, resource)",
        "migration_note": "No migration needed - single version"
      },
      "dotnet": {
        "current_method": "oso.Tell(name, args)",
        "deprecated_method": null,
        "description": "Adds a fact named 'name' with the provided arguments",
        "example": "await oso.Tell(\"has_role\", new List<Value> { user, role, resource})",
        "migration_note": "No migration needed - single version"
      }
    },
    "delete_fact": {
      "nodejs": {
        "current_method": "oso.delete([name, ...args])",
        "deprecated_method": "oso.delete(name, ...args)",
        "description": "Deletes a fact. Supports wildcards with null.",
        "example": "await oso.delete([\"has_role\", user, null, null])",
        "wildcard_support": "null for wildcards"
      },
      "python": {
        "current_method": "oso.delete((name, *args))",
        "deprecated_method": "oso.delete(name, *args)",
        "description": "Deletes a fact. Supports wildcards with None and ValueOfType.",
        "example": "oso.delete((\"has_role\", user, None, ValueOfType(\"Repository\")))",
        "wildcard_support": "None for wildcards, ValueOfType for type constraints"
      },
      "go": {
        "current_method": "osoClient.Delete(pattern)",
        "deprecated_method": "osoClient.Delete(name, ...args)",
        "description": "Deletes facts matching the given pattern",
        "example": "e := osoClient.Delete(oso.NewFactPattern(\"has_role\", user, nil, oso.NewValueOfType(\"Repo\")))",
        "wildcard_support": "nil for wildcards, ValueOfType for type constraints"
      },
      "java": {
        "current_method": "oso.delete(Fact/FactPattern)",
        "deprecated_method": "oso.delete(name, ...args)",
        "description": "Deletes facts. Supports patterns with wildcards.",
        "example": "oso.delete(new FactPattern(\"has_role\", user, ValuePattern.ANY, new ValuePattern.ValueOfType(\"Organization\")))",
        "wildcard_support": "ValuePattern.ANY for wildcards, ValuePattern.ValueOfType for type constraints"
      },
      "ruby": {
        "current_method": "oso.delete(name, *args)",
        "deprecated_method": null,
        "description": "Deletes a fact. Does not throw error if not found.",
        "example": "oso.delete(\"has_role\", user, \"maintainer\", repo)",
        "wildcard_support": "nil for wildcards"
      },
      "dotnet": {
        "current_method": "oso.Delete(name, args)",
        "deprecated_method": null,
        "description": "Deletes a fact. Does not throw error if not found.",
        "example": "oso.Delete(\"has_role\", new List<Value> { user, new Value(\"String\", \"maintainer\"), repo })",
        "wildcard_support": "null for wildcards"
      }
    },
    "get_facts": {
      "nodejs": {
        "current_method": "oso.get([name, ...args])",
        "deprecated_method": "oso.get(name, ...args)",
        "description": "Get facts stored in Oso Cloud. Supports wildcards.",
        "example": "await oso.get([\"has_role\", null, null, repo])",
        "wildcard_support": "null for wildcards"
      },
      "python": {
        "current_method": "oso.get((name, *args))",
        "deprecated_method": "oso.get(name, *args)",
        "description": "Get facts stored in Oso Cloud. Supports wildcards and type constraints.",
        "example": "oso.get((\"has_role\", None, None, ValueOfType(\"Repository\")))",
        "wildcard_support": "None for wildcards, ValueOfType for type constraints"
      },
      "go": {
        "current_method": "osoClient.Get(pattern)",
        "deprecated_method": "osoClient.Get(name, ...args)",
        "description": "Lists facts stored in Oso Cloud using patterns",
        "example": "osoClient.Get(oso.NewFactPattern(\"has_role\", nil, nil, oso.NewValueOfType(\"Repository\")))",
        "wildcard_support": "nil for wildcards, ValueOfType for type constraints"
      },
      "java": {
        "current_method": "oso.get(FactPattern)",
        "deprecated_method": "oso.get(name, ...args)",
        "description": "Lists facts using patterns with wildcard support",
        "example": "Fact[] facts = oso.get(new FactPattern(\"has_role\", user, ValuePattern.ANY, ValuePattern.ANY))",
        "wildcard_support": "ValuePattern.ANY for wildcards, ValuePattern.ValueOfType for type constraints"
      },
      "ruby": {
        "current_method": "oso.get(name, *args)",
        "deprecated_method": null,
        "description": "Lists facts stored in Oso Cloud. Supports wildcards.",
        "example": "oso.get(\"has_role\", nil, nil, repo)",
        "wildcard_support": "nil for wildcards"
      },
      "dotnet": {
        "current_method": "oso.Get(name, args)",
        "deprecated_method": null,
        "description": "Lists facts stored in Oso Cloud. Supports wildcards.",
        "example": "oso.Get(\"has_role\", new List<Value> { new Value(\"User\", null), new Value(\"String\", null), repo })",
        "wildcard_support": "null for wildcards"
      }
    },
    "batch_operations": {
      "nodejs": {
        "method": "oso.batch((tx) => { tx.insert(fact); tx.delete(fact); })",
        "description": "Transactionally delete and insert facts in atomic operation",
        "example": "await oso.batch((tx) => { tx.insert([\"has_role\", user, \"member\", repo]); tx.delete([\"has_role\", user, \"admin\", repo]); });",
        "return_type": "Promise<void>"
      },
      "python": {
        "method": "with oso.batch() as tx: tx.insert(fact); tx.delete(fact)",
        "description": "Context manager for batch insert and delete operations",
        "example": "with oso.batch() as tx:\n    tx.insert((\"has_role\", user, \"member\", repo))\n    tx.delete((\"has_role\", user, \"admin\", repo))",
        "return_type": "None"
      },
      "go": {
        "method": "osoClient.Batch(func(tx BatchTransaction) { tx.Insert(fact); tx.Delete(fact); })",
        "description": "Batch together many inserts and deletes into single HTTP call",
        "example": "osoClient.Batch(func(tx BatchTransaction) {\n    tx.Insert(oso.NewFact(\"has_role\", user, \"member\", repo))\n    tx.Delete(oso.NewFactPattern(\"has_role\", user, \"admin\", repo))\n})",
        "return_type": "error"
      },
      "java": {
        "method": "Not available",
        "description": "Java SDK does not have batch operations",
        "example": "Use individual insert/delete calls",
        "return_type": "N/A"
      },
      "ruby": {
        "method": "oso.bulk(delete: [facts], insert: [facts])",
        "description": "Transactionally delete and insert facts. Deletes processed before inserts.",
        "example": "oso.bulk(delete: [[\"has_role\", user, nil, nil]], insert: [[\"has_role\", user, \"member\", repo]])",
        "return_type": "nil"
      },
      "dotnet": {
        "method": "oso.Bulk(deleteFacts, insertFacts)",
        "description": "Transactionally delete and insert facts in single operation",
        "example": "await oso.Bulk(new List<Fact> { deleteRole }, new List<Fact> { insertRole });",
        "return_type": "Task"
      }
    },
    "bulk_operations": {
      "ruby": {
        "bulk_tell_method": "oso.bulk_tell(facts)",
        "bulk_tell_description": "Adds many facts at once",
        "bulk_tell_example": "oso.bulk_tell([[\"has_role\", user1, \"member\", repo], [\"has_role\", user2, \"admin\", repo]])",
        "bulk_delete_method": "oso.bulk_delete(facts)",
        "bulk_delete_description": "Deletes many facts at once. Does not error if facts not found.",
        "bulk_delete_example": "oso.bulk_delete([[\"has_role\", user1, \"member\", repo], [\"has_role\", user2, \"admin\", repo]])",
        "return_type": "nil"
      },
      "dotnet": {
        "bulk_tell_method": "oso.BulkTell(facts)",
        "bulk_tell_description": "Adds many facts at once",
        "bulk_tell_example": "await oso.BulkTell(new List<Fact> { role1, role2 });",
        "bulk_delete_method": "oso.BulkDelete(facts)",
        "bulk_delete_description": "Deletes many facts at once. Does not error if facts not found.",
        "bulk_delete_example": "await oso.BulkDelete(new List<Fact> { role1, role2 });",
        "return_type": "Task"
      }
    }
  }
}
```

### 1c: Authorization Checks Content

```json
{
  "authorization_checks": {
    "basic_authorize": {
      "nodejs": {
        "method": "oso.authorize(actor, action, resource, [contextFacts])",
        "description": "Determines whether an action is allowed",
        "example": "const allowed = await oso.authorize(alice, \"read\", repo);",
        "context_facts_support": true,
        "return_type": "Promise<boolean>"
      },
      "python": {
        "method": "oso.authorize(actor, action, resource, [contextFacts])",
        "description": "Determines whether an action is allowed",
        "example": "if not oso.authorize(alice, \"read\", repo): raise Exception(\"Not allowed\")",
        "context_facts_support": true,
        "return_type": "boolean"
      },
      "go": {
        "method": "osoClient.Authorize(actor, action, resource)",
        "description": "Determines whether an action is allowed",
        "example": "allowed, e := osoClient.Authorize(user, \"read\", repo)",
        "context_facts_support": true,
        "return_type": "(bool, error)"
      },
      "java": {
        "method": "oso.authorize(actor, action, resource, [contextFacts])",
        "description": "Determines whether an action is allowed",
        "example": "boolean allowed = oso.authorize(user, \"read\", repo);",
        "context_facts_support": true,
        "return_type": "boolean"
      },
      "ruby": {
        "method": "oso.authorize(actor, action, resource, [contextFacts])",
        "description": "Determines whether an action is allowed",
        "example": "raise \"Not allowed\" unless oso.authorize(alice, \"read\", repo)",
        "context_facts_support": true,
        "return_type": "boolean"
      },
      "dotnet": {
        "method": "oso.Authorize(actor, action, resource, [contextFacts])",
        "description": "Determines whether an action is allowed",
        "example": "var allowed = oso.Authorize(user, \"read\", repo);",
        "context_facts_support": true,
        "return_type": "boolean"
      }
    },
    "list_resources": {
      "nodejs": {
        "method": "oso.list(actor, action, resourceType, [contextFacts])",
        "description": "Fetches list of resource IDs on which actor can perform action",
        "example": "const repoIds = await oso.list(alice, \"read\", \"Repository\");",
        "return_type": "Promise<string[]>"
      },
      "python": {
        "method": "oso.list(actor, action, resourceType, [contextFacts])",
        "description": "Fetches list of resources on which actor can perform action",
        "example": "repo_ids = oso.list(alice, \"read\", \"Repository\")",
        "return_type": "List[str]"
      },
      "go": {
        "method": "osoClient.List(actor, action, resourceType, nil)",
        "description": "Fetches list of resource IDs on which actor can perform action",
        "example": "repoIds, e := osoClient.List(user, \"read\", \"Repository\", nil)",
        "return_type": "([]string, error)"
      },
      "java": {
        "method": "oso.list(actor, action, resourceType, [contextFacts])",
        "description": "Fetches list of resource IDs on which actor can perform action",
        "example": "String[] repoIds = oso.list(user, \"read\", \"Repository\");",
        "return_type": "String[]"
      },
      "ruby": {
        "method": "oso.list(actor, action, resourceType, [contextFacts])",
        "description": "Fetches list of resource ids on which actor can perform action",
        "example": "repo_ids = oso.list(alice, \"read\", \"Repository\")",
        "return_type": "Array<String>"
      },
      "dotnet": {
        "method": "oso.List(actor, action, resourceType)",
        "description": "Fetches list of resource IDs on which actor can perform action",
        "example": "var repoIds = oso.List(user, \"read\", \"Repository\");",
        "return_type": "List<string>"
      }
    },
    "authorize_resources": {
      "ruby": {
        "method": "oso.authorize_resources(actor, action, resources, [contextFacts])",
        "description": "Returns a subset of resources which an actor can perform a particular action on",
        "example": "authorized_repos = oso.authorize_resources(alice, \"read\", [repo1, repo2])",
        "return_type": "Array<OsoCloud::Value>"
      },
      "dotnet": {
        "method": "oso.AuthorizeResources(actor, action, resources, [contextFacts])",
        "description": "Returns a subset of resources which an actor can perform a particular action on",
        "example": "var authorizedRepos = await oso.AuthorizeResources(alice, \"read\", new List<Value> { repo1, repo2 });",
        "return_type": "Task<List<Value>>"
      }
    },
    "list_actions": {
      "nodejs": {
        "method": "oso.actions(actor, resource, [contextFacts])",
        "description": "Fetches list of actions actor can perform on resource",
        "example": "const actions = await oso.actions(alice, repo);",
        "return_type": "Promise<string[]>"
      },
      "python": {
        "method": "oso.actions(actor, resource, [contextFacts])",
        "description": "Fetches list of actions actor can perform on resource",
        "example": "actions = oso.actions(alice, repo)",
        "return_type": "List[str]"
      },
      "go": {
        "method": "osoClient.Actions(actor, resource)",
        "description": "Fetches list of actions actor can perform on resource",
        "example": "actions, e := osoClient.Actions(user, repo)",
        "return_type": "([]string, error)"
      },
      "java": {
        "method": "oso.actions(actor, resource, [contextFacts])",
        "description": "Fetches list of actions actor can perform on resource",
        "example": "String[] actions = oso.actions(user, repo);",
        "return_type": "String[]"
      },
      "ruby": {
        "method": "oso.actions(actor, resource, [contextFacts])",
        "description": "Fetches list of actions actor can perform on resource",
        "example": "actions = oso.actions(alice, repo)",
        "return_type": "Array<String>"
      },
      "dotnet": {
        "method": "oso.Actions(actor, resource)",
        "description": "Fetches list of actions actor can perform on resource",
        "example": "var actions = oso.Actions(user, repo);",
        "return_type": "List<string>"
      }
    }
  },
  "query_builder": {
    "build_query": {
      "nodejs": {
        "current_method": "oso.buildQuery([predicate, ...args])",
        "deprecated_method": "oso.query(predicate, ...args)",
        "description": "Query builder API for complex queries with fluent interface",
        "example": "await oso.buildQuery([\"allow\", actor, \"read\", repository]).evaluate(repository)",
        "fluent_methods": [".and()", ".in()", ".withContextFacts()", ".evaluate()"],
        "migration_note": "Replaced simple query with powerful QueryBuilder API"
      },
      "python": {
        "current_method": "oso.build_query((predicate, *args))",
        "deprecated_method": "oso.query(predicate, *args)",
        "description": "Query builder API for complex queries with fluent interface",
        "example": "oso.build_query((\"allow\", actor, \"read\", repository)).evaluate(repository)",
        "fluent_methods": [".and_()", ".in_()", ".with_context_facts()", ".evaluate()"],
        "migration_note": "Replaced simple query with powerful QueryBuilder API"
      },
      "go": {
        "current_method": "osoClient.BuildQuery(queryFact)",
        "deprecated_method": "osoClient.Query(predicate, ...args)",
        "description": "Query builder API for complex queries with fluent interface",
        "example": "osoClient.BuildQuery(oso.NewQueryFact(\"allow\", actor, oso.String(\"read\"), repository)).EvaluateValues(repository)",
        "fluent_methods": [".And()", ".In()", ".WithContextFacts()", ".EvaluateValues()"],
        "migration_note": "Replaced simple query with powerful QueryBuilder API"
      },
      "java": {
        "current_method": "oso.buildQuery(predicate, ...args)",
        "deprecated_method": "oso.query(predicate, ...args)",
        "description": "Query builder API for complex queries with fluent interface",
        "example": "oso.buildQuery(\"allow\", actor, action, repository).evaluate(EvaluateArgs.values(repository))",
        "fluent_methods": [".and()", ".in()", ".withContextFacts()", ".evaluate()"],
        "migration_note": "New QueryBuilder API with flexible evaluation options"
      },
      "ruby": {
        "current_method": "oso.query(rule)",
        "deprecated_method": null,
        "description": "Query for any predicate with concrete and wildcard arguments",
        "example": "oso.query([\"allow\", actor, \"read\", { \"type\" => \"Repository\" }])",
        "fluent_methods": [],
        "migration_note": "No migration needed - single version"
      },
      "dotnet": {
        "current_method": "oso.Query(rule)",
        "deprecated_method": null,
        "description": "Query for any predicate with concrete and wildcard arguments",
        "example": "oso.Query(rule)",
        "fluent_methods": [],
        "migration_note": "No migration needed - single version"
      }
    },
    "complexity_differences": {
      "nodejs": "Full QueryBuilder with TypeScript type generation support",
      "python": "Full QueryBuilder with comprehensive evaluation options",
      "go": "Full QueryBuilder with typed variables and multiple evaluation methods",
      "java": "Most sophisticated QueryBuilder with nested map evaluation and EvaluateArgs system",
      "ruby": "Simple query interface without builder pattern",
      "dotnet": "Simple query interface without builder pattern"
    }
  },
  "simple_query": {
    "ruby": {
      "method": "oso.query(name, *args, context_facts: [])",
      "description": "Lists facts stored in Oso Cloud plus derived facts from policy evaluation",
      "example": "oso.query(\"allow\", actor, \"read\", { \"type\" => \"Repository\" }, context_facts: [])",
      "return_type": "Array<fact>",
      "wildcard_support": "nil for wildcards"
    },
    "dotnet": {
      "method": "oso.Query(predicate, args, [contextFacts])",
      "description": "Lists facts stored in Oso Cloud plus derived facts from policy evaluation",
      "example": "var results = await oso.Query(\"allow\", new List<Value> { actor, action, resource });",
      "return_type": "Task<List<Fact>>",
      "wildcard_support": "null for wildcards"
    }
  }
}
```

### 1d: Local Authorization Content

```json
{
  "local_authorization": {
    "list_local": {
      "nodejs": {
        "method": "oso.listLocal(actor, action, resourceType, column)",
        "description": "Fetches filter for database query to return authorized resources",
        "example": "const filter = await oso.listLocal(alice, \"read\", \"Repository\", \"id\");",
        "database_integration": "Kysely examples provided",
        "return_type": "Promise<string> (SQL filter)"
      },
      "python": {
        "method": "oso.list_local(actor, action, resourceType, column)",
        "description": "Fetches filter for database query to return authorized resources",
        "example": "filter = oso.list_local(alice, \"read\", \"Repository\", \"id\")",
        "database_integration": "SQLAlchemy examples provided",
        "return_type": "str (SQL filter)"
      },
      "go": {
        "method": "osoClient.ListLocal(actor, action, resourceType, column)",
        "description": "Fetches filter for database query to return authorized resources",
        "example": "filter, err := osoClient.ListLocal(alice, \"read\", \"Repository\", \"id\")",
        "database_integration": "GORM examples provided",
        "return_type": "(string, error) (SQL filter)"
      },
      "java": {
        "method": "oso.listLocal(actor, action, resourceType, column)",
        "description": "Fetches SQL query fragment for database WHERE clause",
        "example": "String filterSql = oso.listLocal(alice, \"read\", \"Repository\", \"id\");",
        "database_integration": "JPA examples provided",
        "return_type": "String (SQL fragment)"
      },
      "ruby": {
        "method": "oso.list_local(actor, action, resourceType, column)",
        "description": "Fetches filter for database query to return authorized resources",
        "example": "filter = oso.list_local(alice, \"read\", \"Repository\", \"id\")",
        "database_integration": "Ruby on Rails examples provided",
        "return_type": "String (SQL filter)"
      },
      "dotnet": {
        "method": "oso.ListLocal(actor, action, resourceType, column)",
        "description": "Fetches filter for database query to return authorized resources",
        "example": "var filter = (await oso.ListLocal(alice, \"read\", \"Repository\", \"id\")).Sql;",
        "database_integration": "Entity Framework examples implied",
        "return_type": "Task<LocalAuthResult> (.Sql property)"
      }
    },
    "authorize_local": {
      "nodejs": {
        "method": "oso.authorizeLocal(actor, action, resource)",
        "description": "Fetches query to determine if actor can perform action on resource",
        "example": "const query = await oso.authorizeLocal(alice, \"read\", repo);",
        "return_type": "Promise<string> (SQL query)"
      },
      "python": {
        "method": "oso.authorize_local(actor, action, resource)",
        "description": "Fetches query to determine if actor can perform action on resource",
        "example": "query = oso.authorize_local(alice, \"read\", repo)",
        "return_type": "str (SQL query)"
      },
      "go": {
        "method": "osoClient.AuthorizeLocal(actor, action, resource)",
        "description": "Fetches query to determine if actor can perform action on resource",
        "example": "result, err := osoClient.AuthorizeLocal(alice, \"read\", repo)",
        "return_type": "(AuthorizeLocalResult, error)"
      },
      "java": {
        "method": "oso.authorizeLocal(actor, action, resource)",
        "description": "Fetches complete SQL query returning single boolean 'allowed' column",
        "example": "String sqlQuery = oso.authorizeLocal(alice, \"read\", repo);",
        "return_type": "String (complete SQL query)"
      },
      "ruby": {
        "method": "oso.authorize_local(actor, action, resource)",
        "description": "Fetches query to determine if actor can perform action on resource",
        "example": "query = oso.authorize_local(alice, \"read\", repo)",
        "return_type": "String (SQL query)"
      },
      "dotnet": {
        "method": "oso.AuthorizeLocal(actor, action, resource)",
        "description": "Fetches query to determine if actor can perform action on resource",
        "example": "var sql = (await oso.AuthorizeLocal(alice, \"read\", repo)).Sql;",
        "return_type": "Task<LocalAuthResult> (.Sql property)"
      }
    },
    "actions_local": {
      "nodejs": {
        "method": "oso.actionsLocal(actor, resource)",
        "description": "Fetches query to get actions actor can perform on resource",
        "example": "const query = await oso.actionsLocal(alice, repo);",
        "return_type": "Promise<string> (SQL query)"
      },
      "python": {
        "method": "oso.actions_local(actor, resource)",
        "description": "Fetches query to get actions actor can perform on resource",
        "example": "query = oso.actions_local(alice, repo)",
        "return_type": "str (SQL query)"
      },
      "go": {
        "method": "osoClient.ActionsLocal(actor, resource)",
        "description": "Fetches query to get actions actor can perform on resource",
        "example": "result, err := osoClient.ActionsLocal(alice, repo)",
        "return_type": "(ActionsLocalResult, error)"
      },
      "java": {
        "method": "oso.actionsLocal(actor, resource)",
        "description": "Fetches complete SQL query returning multiple rows with 'action' column",
        "example": "String sqlQuery = oso.actionsLocal(alice, repo);",
        "return_type": "String (complete SQL query)"
      },
      "ruby": {
        "method": "oso.actions_local(actor, resource)",
        "description": "Fetches query to get actions actor can perform on resource",
        "example": "query = oso.actions_local(alice, repo)",
        "return_type": "String (SQL query)"
      },
      "dotnet": {
        "method": "oso.ActionsLocal(actor, resource)",
        "description": "Fetches query to get actions actor can perform on resource",
        "example": "var sql = (await oso.ActionsLocal(alice, repo)).Sql;",
        "return_type": "Task<LocalAuthResult> (.Sql property)"
      }
    },
    "language_specific_differences": {
      "nodejs": "Kysely integration examples, Promise-based async",
      "python": "SQLAlchemy integration examples, synchronous",
      "go": "GORM integration examples, error handling pattern",
      "java": "JPA integration examples, complete SQL queries vs fragments",
      "ruby": "Ruby on Rails integration examples, ActiveRecord patterns",
      "dotnet": "Entity Framework implied, async Task-based with .Sql property access"
    }
  }
}
```

### 1e: Migration Content

```json
{
  "migration_guides": {
    "nodejs_v1_to_v2": {
      "breaking_changes": [
        {
          "category": "Node.js Version",
          "change": "Minimum Node.js version now 16+",
          "before": "Node.js 14+",
          "after": "Node.js 16+",
          "migration_step": "Upgrade Node.js runtime"
        },
        {
          "category": "Centralized Authorization Data API",
          "change": "tell() → insert()",
          "before": "await oso.tell(\"has_role\", user, \"member\", repo);",
          "after": "await oso.insert([\"has_role\", user, \"member\", repo]);",
          "migration_step": "Wrap arguments in array"
        },
        {
          "category": "Centralized Authorization Data API",
          "change": "delete() argument wrapping",
          "before": "await oso.delete(\"has_role\", user, \"member\", repo);",
          "after": "await oso.delete([\"has_role\", user, \"member\", repo]);",
          "migration_step": "Wrap arguments in array, now supports wildcards"
        },
        {
          "category": "Centralized Authorization Data API",
          "change": "bulk() → batch()",
          "before": "await oso.bulk([[\"has_role\", user, null, null]], [[\"has_role\", user, \"member\", repo]]);",
          "after": "await oso.batch((tx) => { tx.delete([\"has_role\", user, null, null]); tx.insert([\"has_role\", user, \"member\", repo]); });",
          "migration_step": "Use transaction callback pattern"
        },
        {
          "category": "Query API",
          "change": "query() → buildQuery()",
          "before": "const results = await oso.query(\"has_role\", user, \"reader\", repo);",
          "after": "const ok = await oso.buildQuery([\"has_role\", user, \"reader\", repo]).evaluate();",
          "migration_step": "Use QueryBuilder API with fluent interface"
        }
      ],
      "new_features": [
        "TypeScript type generation from Polar policies",
        "Enhanced QueryBuilder API with fluent interface",
        "Better wildcard support in delete operations"
      ]
    },
    "python_v1_to_v2": {
      "breaking_changes": [
        {
          "category": "Fact Representation",
          "change": "Dict format → Tuple format",
          "before": "context_fact = {\"name\": \"has_role\", \"args\": [user, \"member\", repo]}",
          "after": "context_fact = (\"has_role\", user, \"member\", repo)",
          "migration_step": "Convert all fact representations to tuples"
        },
        {
          "category": "Value Representation",
          "change": "Dict format → Value objects",
          "before": "alice = {\"type\": \"User\", \"id\": \"alice\"}",
          "after": "from oso_cloud import Value; alice = Value(\"User\", \"alice\")",
          "migration_step": "Use Value objects instead of dicts"
        },
        {
          "category": "Management API",
          "change": "tell() → insert()",
          "before": "oso.tell(\"has_role\", user, \"member\", repo)",
          "after": "oso.insert((\"has_role\", user, \"member\", repo))",
          "migration_step": "Use tuple format and new method name"
        },
        {
          "category": "Query API",
          "change": "query() → build_query()",
          "before": "results = oso.query(\"allow\", actor, \"read\", repository)",
          "after": "results = oso.build_query((\"allow\", actor, \"read\", repository)).evaluate(repository)",
          "migration_step": "Use QueryBuilder API with evaluation"
        }
      ],
      "new_features": [
        "Enhanced QueryBuilder API",
        "Better type checking with IntoValue and IntoFact types",
        "Context manager for batch operations"
      ]
    },
    "go_v1_to_v2": {
      "breaking_changes": [
        {
          "category": "Import Path",
          "change": "Module import path update",
          "before": "import (oso \"github.com/osohq/go-oso-cloud\")",
          "after": "import (oso \"github.com/osohq/go-oso-cloud/v2\")",
          "migration_step": "Add /v2 to all import paths"
        },
        {
          "category": "Struct Changes",
          "change": "Fact.Name → Fact.Predicate",
          "before": "fact.Name",
          "after": "fact.Predicate",
          "migration_step": "Update field references"
        },
        {
          "category": "Struct Changes",
          "change": "Instance → Value",
          "before": "oso.Instance{Type: \"User\", ID: \"bob\"}",
          "after": "oso.NewValue(\"User\", \"bob\")",
          "migration_step": "Replace Instance with Value, use helper functions"
        },
        {
          "category": "API Changes",
          "change": "Tell() → Insert()",
          "before": "osoClient.Tell(\"has_role\", user, role, org)",
          "after": "osoClient.Insert(oso.NewFact(\"has_role\", user, role, org))",
          "migration_step": "Use oso.NewFact() helper"
        },
        {
          "category": "API Changes",
          "change": "Delete() with patterns",
          "before": "osoClient.Delete(\"has_role\", user, role, repo)",
          "after": "osoClient.Delete(oso.NewFactPattern(\"has_role\", user, role, repo))",
          "migration_step": "Use oso.NewFactPattern() helper"
        }
      ],
      "new_features": [
        "FactPattern type for explicit pattern matching",
        "Helper functions for ergonomic construction",
        "Enhanced wildcard and type constraint support"
      ]
    },
    "java_v0_to_v1": {
      "breaking_changes": [
        {
          "category": "Package Structure",
          "change": "API consolidation",
          "before": "import com.osohq.oso_cloud.api.Value;",
          "after": "import com.osohq.oso_cloud.Value;",
          "migration_step": "Update import statements to new package structure"
        },
        {
          "category": "Constructor Validation",
          "change": "No null values in constructors",
          "before": "new Value(\"User\", null) // for wildcards",
          "after": "new FactPattern(\"has_role\", user, ValuePattern.ANY, ValuePattern.ANY)",
          "migration_step": "Use ValuePattern.ANY for wildcards instead of null"
        },
        {
          "category": "API Changes",
          "change": "tell() → insert()",
          "before": "oso.tell(\"has_role\", user, new Value(\"member\"), repo);",
          "after": "oso.insert(new Fact(\"has_role\", user, new Value(\"member\"), repo));",
          "migration_step": "Wrap arguments in Fact object"
        },
        {
          "category": "API Changes",
          "change": "get() with FactPattern",
          "before": "oso.get(\"has_role\", user, null, null)",
          "after": "oso.get(new FactPattern(\"has_role\", user, ValuePattern.ANY, ValuePattern.ANY))",
          "migration_step": "Use FactPattern with explicit wildcards"
        }
      ],
      "new_features": [
        "Flexible Query Builder API with fluent interface",
        "Enhanced wildcard support with ValuePattern",
        "Consolidated package structure",
        "EvaluateArgs system for complex query evaluation"
      ]
    },
    "validation_summary": {
      "all_languages_represented": {
        "policy_api": ["nodejs", "python", "go", "java", "ruby", "dotnet"],
        "facts_api": ["nodejs", "python", "go", "java", "ruby", "dotnet"],
        "batch_operations": ["nodejs", "python", "go", "ruby", "dotnet"],
        "bulk_operations": ["ruby", "dotnet"],
        "authorization_checks": ["nodejs", "python", "go", "java", "ruby", "dotnet"],
        "authorize_resources": ["ruby", "dotnet"],
        "simple_query": ["ruby", "dotnet"],
        "local_authorization": ["nodejs", "python", "go", "java", "ruby", "dotnet"]
      },
      "migration_mappings_captured": {
        "tell_to_insert": ["nodejs", "python", "go", "java"],
        "query_to_build_query": ["nodejs", "python", "go", "java"],
        "bulk_to_batch": ["nodejs", "python", "go", "java"],
        "wildcard_improvements": ["nodejs", "python", "go", "java"]
      },
      "breaking_changes_with_examples": {
        "nodejs": 4,
        "python": 4,
        "go": 5,
        "java": 4
      },
      "query_builder_complexity_noted": {
        "full_featured": ["nodejs", "python", "go", "java"],
        "simple_query": ["ruby", "dotnet"]
      },
      "local_auth_differences_captured": {
        "database_integrations": ["Kysely", "SQLAlchemy", "GORM", "JPA", "Rails", "Entity Framework"],
        "return_type_variations": ["Promise<string>", "str", "(string, error)", "String", "String", "Task<LocalAuthResult>"]
      }
    }
  }
}
```
